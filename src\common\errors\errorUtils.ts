import {
  AppError,
  DatabaseError,
  DatabaseConnectionError,
  DatabaseQueryError,
  DatabaseValidationError,
  DatabaseDuplicateError,
  AuthenticationError,
  TokenExpiredError,
  TokenInvalidError,
  ValidationError,
  ResourceNotFoundError,
  type ErrorContext
} from './index'

// Constants
const MONGO_ERROR_CODES = {
  DUPLICATE_KEY: 11000
} as const

const DEFAULT_ERROR_MESSAGES = {
  UNKNOWN: 'An unknown error occurred',
  GENERIC: 'An error occurred',
  TOKEN_NOT_ACTIVE: 'Token not active yet',
  DUPLICATE_ENTRY: 'Duplicate entry found'
} as const

const HTTP_STATUS_CODES = {
  INTERNAL_SERVER_ERROR: 500
} as const

// Interfaces
interface MongoError {
  code?: number
  name?: string
  message?: string
  kind?: string
  path?: string
  value?: unknown
  errors?: Record<string, MongoValidationError>
}

interface MongoValidationError {
  path: string
  kind: string
  message?: string
}

// Utility functions
/**
 * Creates a standardized error context with timestamp
 */
const createErrorContext = (
  cause: unknown,
  additionalDetails?: Record<string, unknown>
): ErrorContext => {
  return {
    cause,
    timestamp: new Date().toISOString(),
    ...additionalDetails
  }
}

/**
 * Extracts the duplicate field name from MongoDB duplicate key error message
 * Expected format: "dup key: { fieldName: 'value' }"
 */
const extractDuplicateField = (errorMessage: string): string | null => {
  if (!errorMessage || typeof errorMessage !== 'string') {
    return null
  }

  const DUPLICATE_KEY_PATTERN = /dup key: \{\s*([^:]+)\s*:/
  const match = errorMessage.match(DUPLICATE_KEY_PATTERN)

  if (!match || !match[1]) {
    return null
  }

  // Clean up field name by removing quotes and whitespace
  return match[1].replace(/['"]/g, '').trim()
}

/**
 * Creates an error instance with proper context
 */
const createErrorWithContext = <T extends AppError>(
  ErrorClass: new (message: string, context?: ErrorContext) => T,
  message: string,
  context: ErrorContext
): T => {
  return new ErrorClass(message, context)
}

/**
 * Maps error names to their corresponding error classes
 */
const getErrorClassByName = (
  errorName: string
): (new (message: string, context?: ErrorContext) => AppError) | null => {
  const errorMappings: Record<
    string,
    new (message: string, context?: ErrorContext) => AppError
  > = {
    MongoError: DatabaseQueryError,
    MongoServerError: DatabaseQueryError,
    MongoNetworkError: DatabaseConnectionError,
    ValidationError: DatabaseValidationError,
    JsonWebTokenError: TokenInvalidError,
    TokenExpiredError: TokenExpiredError
  }

  return errorMappings[errorName] || null
}

/**
 * Transforms an error by its name using predefined mappings
 */
const transformByErrorName = (
  error: Error,
  context: ErrorContext
): AppError | null => {
  const ErrorClass = getErrorClassByName(error.name)

  if (!ErrorClass) {
    return null
  }

  // Special case for NotBeforeError
  if (error.name === 'NotBeforeError') {
    return createErrorWithContext(
      TokenInvalidError,
      DEFAULT_ERROR_MESSAGES.TOKEN_NOT_ACTIVE,
      context
    )
  }

  return createErrorWithContext(ErrorClass, error.message, context)
}

/**
 * Extracts validation error messages from MongoDB validation errors
 */
const extractValidationMessages = (
  errors: Record<string, MongoValidationError>
): string[] => {
  const messages: string[] = []

  for (const [, err] of Object.entries(errors)) {
    if (!err.path) continue

    const fieldName = err.path
    const errorType = err.kind === 'regexp' ? 'invalid' : err.kind
    messages.push(`The field '${fieldName}' is ${errorType}`)
  }

  return messages
}

/**
 * Type guard to check if an error is an AppError
 * @param error - The error to check
 * @returns True if the error is an instance of AppError
 * @example
 * ```typescript
 * if (isAppError(error)) {
 *   console.log(error.statusCode); // TypeScript knows this is safe
 * }
 * ```
 */
export const isAppError = (error: unknown): error is AppError => {
  return error instanceof AppError
}

/**
 * Type guard to check if an error is a database error
 * @param error - The error to check
 * @returns True if the error is an instance of DatabaseError
 */
export const isDatabaseError = (error: unknown): error is DatabaseError => {
  return error instanceof DatabaseError
}

/**
 * Type guard to check if an error is an authentication error
 * @param error - The error to check
 * @returns True if the error is an instance of AuthenticationError
 */
export const isAuthenticationError = (
  error: unknown
): error is AuthenticationError => {
  return error instanceof AuthenticationError
}

/**
 * Type guard to check if an error is a validation error
 * @param error - The error to check
 * @returns True if the error is an instance of ValidationError
 */
export const isValidationError = (error: unknown): error is ValidationError => {
  return error instanceof ValidationError
}

/**
 * Transform unknown error to appropriate typed error
 * @param error - The error to transform
 * @param defaultMessage - Default message to use if error has no message
 * @returns A properly typed AppError instance
 * @example
 * ```typescript
 * try {
 *   // Some operation that might throw
 * } catch (error) {
 *   const appError = transformError(error, 'Operation failed');
 *   throw appError;
 * }
 * ```
 */
export const transformError = (
  error: unknown,
  defaultMessage?: string
): AppError => {
  // Input validation
  if (error === null || error === undefined) {
    return new AppError(
      defaultMessage || DEFAULT_ERROR_MESSAGES.UNKNOWN,
      HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR
    )
  }

  // If it's already an AppError, return as is
  if (isAppError(error)) {
    return error
  }

  // Handle standard Error objects
  if (error instanceof Error) {
    const context = createErrorContext(error)

    // Try to transform using predefined mappings
    const transformedError = transformByErrorName(error, context)
    if (transformedError) {
      return transformedError
    }

    // Generic Error fallback
    const message =
      error.message || defaultMessage || DEFAULT_ERROR_MESSAGES.GENERIC
    return new AppError(
      message,
      HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR,
      context
    )
  }

  // Handle string errors
  if (typeof error === 'string') {
    const message = error || defaultMessage || DEFAULT_ERROR_MESSAGES.GENERIC
    return new AppError(message)
  }

  // Handle unknown errors
  const message = defaultMessage || DEFAULT_ERROR_MESSAGES.UNKNOWN
  const context = createErrorContext(error)
  return new AppError(message, HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR, context)
}

/**
 * Transform MongoDB specific errors to appropriate typed errors
 * @param error - The MongoDB error to transform
 * @returns A properly typed AppError instance
 * @example
 * ```typescript
 * try {
 *   await User.create({ email: '<EMAIL>' });
 * } catch (error) {
 *   const appError = transformMongoError(error);
 *   throw appError;
 * }
 * ```
 */
export const transformMongoError = (error: MongoError): AppError => {
  // Input validation
  if (!error || typeof error !== 'object') {
    return transformError(error)
  }

  const context = createErrorContext(error)

  // Duplicate key error
  if (error.code === MONGO_ERROR_CODES.DUPLICATE_KEY) {
    if (!error.message) {
      return new DatabaseDuplicateError(
        DEFAULT_ERROR_MESSAGES.DUPLICATE_ENTRY,
        context
      )
    }

    const field = extractDuplicateField(error.message)
    if (field) {
      return new DatabaseDuplicateError(`The ${field} already exists`, context)
    }

    return new DatabaseDuplicateError(
      DEFAULT_ERROR_MESSAGES.DUPLICATE_ENTRY,
      context
    )
  }

  // Cast error (invalid ObjectId)
  if (error.name === 'CastError' && error.kind === 'ObjectId') {
    return new ResourceNotFoundError(
      error.path || 'resource',
      error.value,
      context
    )
  }

  // Validation error
  if (error.name === 'ValidationError' && error.errors) {
    const messages = extractValidationMessages(error.errors)
    const combinedMessage =
      messages.length > 0 ? messages.join(', ') : 'Validation failed'
    return new DatabaseValidationError(combinedMessage, context)
  }

  // Fallback to general error transformation
  return transformError(error)
}

/**
 * Safely extracts an error message from any error type
 * @param error - The error to extract message from
 * @returns The error message or a default message for unknown errors
 * @example
 * ```typescript
 * const message = getErrorMessage(new Error('Something went wrong'));
 * console.log(message); // "Something went wrong"
 *
 * const unknownMessage = getErrorMessage({ someProperty: 'value' });
 * console.log(unknownMessage); // "An unknown error occurred"
 * ```
 */
export const getErrorMessage = (error: unknown): string => {
  // Handle null/undefined
  if (error === null || error === undefined) {
    return DEFAULT_ERROR_MESSAGES.UNKNOWN
  }

  // Handle AppError instances
  if (isAppError(error)) {
    return error.message || DEFAULT_ERROR_MESSAGES.GENERIC
  }

  // Handle standard Error instances
  if (error instanceof Error) {
    return error.message || DEFAULT_ERROR_MESSAGES.GENERIC
  }

  // Handle string errors
  if (typeof error === 'string') {
    return error || DEFAULT_ERROR_MESSAGES.GENERIC
  }

  // Handle unknown error types
  return DEFAULT_ERROR_MESSAGES.UNKNOWN
}

/**
 * Safely extracts an HTTP status code from any error type
 * @param error - The error to extract status code from
 * @returns The HTTP status code or 500 for non-AppError instances
 * @example
 * ```typescript
 * const statusCode = getErrorStatusCode(new AppError('Test', 422));
 * console.log(statusCode); // 422
 *
 * const defaultStatusCode = getErrorStatusCode(new Error('Regular error'));
 * console.log(defaultStatusCode); // 500
 * ```
 */
export const getErrorStatusCode = (error: unknown): number => {
  // Handle null/undefined
  if (error === null || error === undefined) {
    return HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR
  }

  // Handle AppError instances
  if (isAppError(error)) {
    return error.statusCode || HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR
  }

  // Default to 500 for all other error types
  return HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR
}
